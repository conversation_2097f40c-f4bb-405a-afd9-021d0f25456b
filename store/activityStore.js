import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { activityService } from "../services/activityService";
import { highlightsService } from "services/highlightService";

const initialState = {
  // Activity creation/saving state
  isSavingActivity: false,
  savingActivityError: null,

  // All activities state
  isLoadingAllActivities: false,
  allActivities: [],
  totalBurnedCalories: 0,
  allActivitiesError: null,

  // Activity history state
  isLoadingActivityHistory: false,
  activityHistory: [],
  lastLoggedActivity: null,
  activityHistoryError: null,

  // Activity summary/highlights state
  isLoadingActivitySummary: false,
  activitySummary: null,
  activitySummaryError: null,

  // Activity highlights state
  isLoadingActivityHighlights: false,
  activityHighlights: [],
  activityHighlightsError: null,
};

const useActivityStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      // Save a new activity
      saveActivity: async ({ activityType, durationInMinutes }) => {
        set((state) => ({
          ...state,
          isSavingActivity: true,
          savingActivityError: null
        }));

        try {
          const res = await activityService.createActivity({
            activityType,
            durationInMinutes,
          });

          if (res.success) {
            set((state) => ({
              ...state,
              isSavingActivity: false,
              lastLoggedActivity: {
                activityType,
                durationInMinutes,
                createdAt: new Date().toISOString(),
              },
            }));

            // Refresh activity data after saving
            await get().getAllActivities();
            await get().getActivityHistory();

            return {
              success: true,
              message: res.message,
            };
          } else {
            set((state) => ({
              ...state,
              isSavingActivity: false,
              savingActivityError: res.error,
            }));

            return {
              success: false,
              error: res.error,
            };
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isSavingActivity: false,
            savingActivityError: error.message || "Error saving activity",
          }));

          return {
            success: false,
            error: error.message || "Error saving activity",
          };
        }
      },

      // Get all activities
      getAllActivities: async () => {
        set((state) => ({
          ...state,
          isLoadingAllActivities: true,
          allActivitiesError: null
        }));

        try {
          const res = await activityService.getAllActivities();
          console.log("getAllActivities store :", res.data);
          console.log("Activities data ===", res.data?.activities);
          console.log("Total burned calories ===", res.data?.totalBurnedCalories);

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingAllActivities: false,
              allActivities: res.data?.activities || [],
              totalBurnedCalories: res.data?.totalBurnedCalories || 0,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingAllActivities: false,
              allActivitiesError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingAllActivities: false,
            allActivitiesError: error.message || "Error fetching all activities",
          }));
        }
      },

      // Get activity history for current date
      getActivityHistory: async (date = null) => {
        set((state) => ({
          ...state,
          isLoadingActivityHistory: true,
          activityHistoryError: null
        }));

        try {
          const targetDate = date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
          const res = await activityService.getActivityHistory(targetDate);

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingActivityHistory: false,
              activityHistory: res.data,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingActivityHistory: false,
              activityHistoryError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingActivityHistory: false,
            activityHistoryError: error.message || "Error fetching activity history",
          }));
        }
      },

      // Get activity summary for dashboard
      getActivitySummary: async () => {
        set((state) => ({
          ...state,
          isLoadingActivitySummary: true,
          activitySummaryError: null
        }));

        try {
          const res = await activityService.getActivitySummary();

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingActivitySummary: false,
              activitySummary: res.data,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingActivitySummary: false,
              activitySummaryError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingActivitySummary: false,
            activitySummaryError: error.message || "Error fetching activity summary",
          }));
        }
      },

      // Get activity highlights
      getActivityHighlights: async () => {
        set((state) => ({
          ...state,
          isLoadingActivityHighlights: true,
          activityHighlightsError: null
        }));

        try {
          const res = await highlightsService.getHighlights("activity");

          if (res.success) {
            set((state) => ({
              ...state,
              isLoadingActivityHighlights: false,
              activityHighlights: res.data,
            }));
          } else {
            set((state) => ({
              ...state,
              isLoadingActivityHighlights: false,
              activityHighlightsError: res.error,
            }));
          }
        } catch (error) {
          set((state) => ({
            ...state,
            isLoadingActivityHighlights: false,
            activityHighlightsError: error.message || "Error fetching activity highlights",
          }));
        }
      },

      // Clear all errors
      clearActivityStoreErrors: () => {
        set((state) => ({
          ...state,
          savingActivityError: null,
          allActivitiesError: null,
          activityHistoryError: null,
          activitySummaryError: null,
          activityHighlightsError: null,
        }));
      },

      // Reset store to initial state
      resetActivityStore: () => set({ ...initialState }),
    }),
    {
      name: "activity-store",
      storage: createJSONStorage(() => AsyncStorage),
    }
  )
);

export default useActivityStore;
